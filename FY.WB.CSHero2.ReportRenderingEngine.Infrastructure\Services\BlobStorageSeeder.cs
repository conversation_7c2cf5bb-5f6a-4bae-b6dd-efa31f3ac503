using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Service for seeding blob storage with report data from old mock data
    /// </summary>
    public class BlobStorageSeeder
    {
        private readonly IReportDataBlobService _blobService;
        private readonly ILogger<BlobStorageSeeder> _logger;

        public BlobStorageSeeder(IReportDataBlobService blobService, ILogger<BlobStorageSeeder> logger)
        {
            _blobService = blobService ?? throw new ArgumentNullException(nameof(blobService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Seeds blob storage with report data correlated from old mock data
        /// </summary>
        /// <param name="seededReports">List of reports that were seeded in SQL</param>
        /// <param name="seededTenants">List of tenants that were seeded</param>
        /// <returns>Dictionary of report IDs to blob paths</returns>
        public async Task<Dictionary<string, string>> SeedReportDataAsync(
            List<SeededReportInfo> seededReports,
            List<SeededTenantInfo> seededTenants)
        {
            _logger.LogInformation("Starting blob storage seeding for {ReportCount} reports across {TenantCount} tenants",
                seededReports.Count, seededTenants.Count);

            var blobPaths = new Dictionary<string, string>();

            try
            {
                // Load old mock data
                var oldMockData = await LoadOldMockDataAsync();
                if (oldMockData == null)
                {
                    _logger.LogWarning("Could not load old mock data, skipping blob seeding");
                    return blobPaths;
                }

                // Seed dashboard metrics for each tenant
                await SeedDashboardMetricsAsync(seededTenants, oldMockData, blobPaths);

                // Seed report-specific data
                await SeedReportSpecificDataAsync(seededReports, oldMockData, blobPaths);

                // Seed sample assets
                await SeedSampleAssetsAsync(seededReports, blobPaths);

                _logger.LogInformation("Blob storage seeding completed successfully. Created {BlobCount} blobs",
                    blobPaths.Count);

                return blobPaths;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during blob storage seeding");
                throw;
            }
        }

        private async Task<OldMockData?> LoadOldMockDataAsync()
        {
            try
            {
                var filePath = Path.Combine("memory-bank", "old_json_data", "src", "data", "db", "db.json");
                
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("Old mock data file not found at {FilePath}", filePath);
                    return null;
                }

                var jsonContent = await File.ReadAllTextAsync(filePath);
                var mockData = JsonSerializer.Deserialize<OldMockData>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                _logger.LogInformation("Successfully loaded old mock data from {FilePath}", filePath);
                return mockData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading old mock data");
                return null;
            }
        }

        private async Task SeedDashboardMetricsAsync(
            List<SeededTenantInfo> seededTenants,
            OldMockData oldMockData,
            Dictionary<string, string> blobPaths)
        {
            _logger.LogInformation("Seeding dashboard metrics for {TenantCount} tenants", seededTenants.Count);

            foreach (var tenant in seededTenants)
            {
                try
                {
                    // Create dashboard metrics for each time period
                    var dashboardData = new Dictionary<string, object>
                    {
                        ["today"] = oldMockData.Today,
                        ["wtd"] = oldMockData.Wtd,
                        ["mtd"] = oldMockData.Mtd,
                        ["ytd"] = oldMockData.Ytd,
                        ["generatedAt"] = DateTime.UtcNow,
                        ["tenantId"] = tenant.TenantId,
                        ["dataSource"] = "seeded_from_old_mock_data"
                    };

                    // Use a special "dashboard" report ID for tenant-level metrics
                    var dashboardReportId = Guid.NewGuid();
                    var dashboardVersionId = Guid.NewGuid();

                    var blobPath = await _blobService.SaveReportDataAsync(
                        tenant.TenantId,
                        dashboardReportId,
                        dashboardVersionId,
                        dashboardData);

                    blobPaths[$"dashboard-{tenant.TenantId}"] = blobPath;

                    _logger.LogDebug("Created dashboard metrics blob for tenant {TenantId}: {BlobPath}",
                        tenant.TenantId, blobPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error seeding dashboard metrics for tenant {TenantId}", tenant.TenantId);
                }
            }
        }

        private async Task SeedReportSpecificDataAsync(
            List<SeededReportInfo> seededReports,
            OldMockData oldMockData,
            Dictionary<string, string> blobPaths)
        {
            _logger.LogInformation("Seeding report-specific data for {ReportCount} reports", seededReports.Count);

            // Create category mapping from old mock reports to current categories
            var categoryMapping = CreateCategoryMapping(oldMockData.Reports);

            foreach (var report in seededReports)
            {
                try
                {
                    // Find matching old mock data based on category
                    var matchingOldReport = FindMatchingOldReport(report, categoryMapping);
                    
                    // Create report data combining dashboard metrics with report-specific info
                    var reportData = CreateReportData(report, matchingOldReport, oldMockData);

                    // Generate a version ID for this seeded data
                    var versionId = Guid.NewGuid();

                    var blobPath = await _blobService.SaveReportDataAsync(
                        report.TenantId,
                        report.ReportId,
                        versionId,
                        reportData);

                    blobPaths[$"report-{report.ReportId}"] = blobPath;

                    _logger.LogDebug("Created report data blob for report {ReportNumber}: {BlobPath}",
                        report.ReportNumber, blobPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error seeding data for report {ReportNumber}", report.ReportNumber);
                }
            }
        }

        private Dictionary<string, List<OldMockReport>> CreateCategoryMapping(List<OldMockReport> oldReports)
        {
            var mapping = new Dictionary<string, List<OldMockReport>>();

            // Group old reports by their categories and map to new categories
            var categoryMap = new Dictionary<string, string>
            {
                ["Satisfaction Survey"] = "Feedback",
                ["Performance Metrics"] = "Performance", 
                ["Action Plan"] = "Strategy",
                ["Channel Analysis"] = "Usage",
                ["User Experience"] = "Usage",
                ["Product Improvement"] = "Performance"
            };

            foreach (var oldReport in oldReports)
            {
                if (categoryMap.TryGetValue(oldReport.Category, out var newCategory))
                {
                    if (!mapping.ContainsKey(newCategory))
                        mapping[newCategory] = new List<OldMockReport>();
                    
                    mapping[newCategory].Add(oldReport);
                }
            }

            return mapping;
        }

        private OldMockReport? FindMatchingOldReport(SeededReportInfo report, Dictionary<string, List<OldMockReport>> categoryMapping)
        {
            if (!categoryMapping.TryGetValue(report.Category, out var oldReports) || !oldReports.Any())
                return null;

            // Use a simple round-robin approach to distribute old reports across new ones
            var index = Math.Abs(report.ReportId.GetHashCode()) % oldReports.Count;
            return oldReports[index];
        }

        private Dictionary<string, object> CreateReportData(
            SeededReportInfo report,
            OldMockReport? matchingOldReport,
            OldMockData oldMockData)
        {
            var reportData = new Dictionary<string, object>
            {
                ["reportId"] = report.ReportId,
                ["reportNumber"] = report.ReportNumber,
                ["reportName"] = report.ReportName,
                ["category"] = report.Category,
                ["tenantId"] = report.TenantId,
                ["clientId"] = report.ClientId,
                ["clientName"] = report.ClientName,
                ["status"] = report.Status,
                ["author"] = report.Author,
                ["slideCount"] = report.SlideCount,
                ["createdAt"] = report.CreationTime,
                ["generatedAt"] = DateTime.UtcNow,
                ["dataSource"] = "seeded_from_old_mock_data"
            };

            // Add dashboard metrics based on report category
            var metricsKey = DetermineMetricsTimeframe(report.Category);
            reportData["dashboardMetrics"] = GetMetricsByTimeframe(oldMockData, metricsKey);

            // Add old report data if available
            if (matchingOldReport != null)
            {
                reportData["originalMockData"] = new Dictionary<string, object>
                {
                    ["originalReportId"] = matchingOldReport.ReportId,
                    ["originalClientName"] = matchingOldReport.ClientName,
                    ["originalCategory"] = matchingOldReport.Category,
                    ["originalSlideCount"] = matchingOldReport.SlideCount,
                    ["originalStatus"] = matchingOldReport.Status,
                    ["originalAuthor"] = matchingOldReport.Author
                };
            }

            // Add sample chart data
            reportData["chartData"] = GenerateSampleChartData(report.Category);

            return reportData;
        }

        private string DetermineMetricsTimeframe(string category)
        {
            // Map categories to appropriate timeframes
            return category switch
            {
                "Feedback" => "today",
                "Performance" => "wtd",
                "Strategy" => "mtd", 
                "Usage" => "ytd",
                _ => "today"
            };
        }

        private object GetMetricsByTimeframe(OldMockData oldMockData, string timeframe)
        {
            return timeframe switch
            {
                "today" => oldMockData.Today,
                "wtd" => oldMockData.Wtd,
                "mtd" => oldMockData.Mtd,
                "ytd" => oldMockData.Ytd,
                _ => oldMockData.Today
            };
        }

        private Dictionary<string, object> GenerateSampleChartData(string category)
        {
            var random = new Random();
            
            return new Dictionary<string, object>
            {
                ["chartType"] = category switch
                {
                    "Feedback" => "pie",
                    "Performance" => "line",
                    "Strategy" => "bar",
                    "Usage" => "area",
                    _ => "line"
                },
                ["dataPoints"] = Enumerable.Range(1, 7).Select(i => new
                {
                    label = $"Day {i}",
                    value = random.Next(10, 100)
                }).ToArray(),
                ["colors"] = category switch
                {
                    "Feedback" => new[] { "#10b981", "#3b82f6", "#f59e0b" },
                    "Performance" => new[] { "#3b82f6", "#1d4ed8" },
                    "Strategy" => new[] { "#f59e0b", "#d97706" },
                    "Usage" => new[] { "#8b5cf6", "#7c3aed" },
                    _ => new[] { "#6b7280", "#4b5563" }
                }
            };
        }

        private async Task SeedSampleAssetsAsync(List<SeededReportInfo> seededReports, Dictionary<string, string> blobPaths)
        {
            _logger.LogInformation("Seeding sample assets for {ReportCount} reports", seededReports.Count);

            foreach (var report in seededReports.Take(3)) // Limit to first 3 reports for demo
            {
                try
                {
                    // Create a simple SVG chart as sample asset
                    var svgContent = GenerateSampleSvgChart(report.Category);
                    using var svgStream = new MemoryStream(Encoding.UTF8.GetBytes(svgContent));

                    var versionId = Guid.NewGuid();
                    var assetPath = await _blobService.SaveAssetAsync(
                        report.TenantId,
                        report.ReportId,
                        versionId,
                        "sample-chart.svg",
                        svgStream,
                        "image/svg+xml");

                    blobPaths[$"asset-{report.ReportId}-chart"] = assetPath;

                    _logger.LogDebug("Created sample asset for report {ReportNumber}: {AssetPath}",
                        report.ReportNumber, assetPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error seeding assets for report {ReportNumber}", report.ReportNumber);
                }
            }
        }

        private string GenerateSampleSvgChart(string category)
        {
            var color = category switch
            {
                "Feedback" => "#10b981",
                "Performance" => "#3b82f6", 
                "Strategy" => "#f59e0b",
                "Usage" => "#8b5cf6",
                _ => "#6b7280"
            };

            return $@"<svg width=""200"" height=""100"" xmlns=""http://www.w3.org/2000/svg"">
  <rect width=""200"" height=""100"" fill=""#f8fafc""/>
  <rect x=""20"" y=""20"" width=""160"" height=""60"" fill=""{color}"" opacity=""0.8""/>
  <text x=""100"" y=""55"" text-anchor=""middle"" fill=""white"" font-family=""Arial"" font-size=""14"">{category} Chart</text>
</svg>";
        }
    }

    // Data transfer objects for seeding
    public class SeededReportInfo
    {
        public Guid ReportId { get; set; }
        public string ReportNumber { get; set; } = string.Empty;
        public string ReportName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public Guid TenantId { get; set; }
        public Guid ClientId { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public DateTime CreationTime { get; set; }
    }

    public class SeededTenantInfo
    {
        public Guid TenantId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string ContactName { get; set; } = string.Empty;
    }

    // Old mock data models
    public class OldMockData
    {
        public MetricsPeriod Today { get; set; } = new();
        public MetricsPeriod Wtd { get; set; } = new();
        public MetricsPeriod Mtd { get; set; } = new();
        public MetricsPeriod Ytd { get; set; } = new();
        public List<OldMockReport> Reports { get; set; } = new();
    }

    public class MetricsPeriod
    {
        public MetricsData Metrics { get; set; } = new();
    }

    public class MetricsData
    {
        public MetricValue TotalCustomers { get; set; } = new();
        public MetricValue NewCustomers { get; set; } = new();
        public MetricValue ReportsCreated { get; set; } = new();
        public MetricValue Revenue { get; set; } = new();
    }

    public class MetricValue
    {
        public int Current { get; set; }
        public int PreviousPeriod { get; set; }
        public List<int> Trend { get; set; } = new();
    }

    public class OldMockReport
    {
        public string ReportId { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public string ClientName { get; set; } = string.Empty;
        public string ReportName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public string CreatedAt { get; set; } = string.Empty;
        public string LastModified { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
    }
}
