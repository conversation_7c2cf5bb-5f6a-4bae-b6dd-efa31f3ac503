using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Infrastructure.Services;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;
using FY.WB.CSHero2.StartupConfiguration;
using FY.WB.CSHero2.Infrastructure.Persistence.Seeders;
using FY.WB.CSHero2.Infrastructure;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

// Get logger factory
var loggerFactory = LoggerFactory.Create(builder =>
{
    builder
        .AddConsole()
        .AddDebug()
        .SetMinimumLevel(LogLevel.Information);
});
var logger = loggerFactory.CreateLogger<Program>();

// Log the connection string
var connectionString = configuration.GetConnectionString("DefaultConnection");
logger.LogInformation("Connection String: {ConnectionString}", connectionString);

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add database configuration
builder.Services.AddInfrastructure(configuration);

// Add identity configuration
builder.Services.AddIdentityConfiguration();

// Add authentication configuration
builder.Services.AddAuthenticationConfiguration(configuration);

// Add multi-tenancy configuration
builder.Services.AddMultiTenancyConfiguration();

// Add MediatR configuration
builder.Services.AddMediatRConfiguration();

// Add HttpContext and current user services
builder.Services.AddHttpContextAccessor();
// ICurrentUserService already registered in Infrastructure

// Add other custom services (like CompanyProfileService)
builder.Services.AddServiceConfiguration();

// Add Report Rendering Engine configuration
builder.Services.AddReportRenderingEngineConfiguration(configuration);

// Add controllers and Swagger
builder.Services.AddControllers();

// Add Swagger/OpenAPI support
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerConfiguration();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    // Add Swagger middleware before other middleware
    app.UseSwaggerConfiguration();
}

// Use CORS middleware
app.UseCors("AllowAll");

// Add routing middleware
app.UseRouting();

// Add multi-tenancy middleware
app.UseMultiTenancyConfiguration();

// Add authentication and authorization middleware
app.UseAuthentication();
app.UseAuthorization();

// Database migration and seeding
logger.LogInformation("Initializing database...");
try
{
    using (var scope = app.Services.CreateScope())
    {
        logger.LogInformation("Created service scope");
        var services = scope.ServiceProvider;

        try
        {
            logger.LogInformation("Getting ApplicationDbContext...");
            var context = services.GetRequiredService<ApplicationDbContext>();
            logger.LogInformation("ApplicationDbContext obtained successfully");

            logger.LogInformation("Checking if database is SQL Server...");
            if (context.Database.IsSqlServer())
            {
                logger.LogInformation("Database is SQL Server, attempting to connect...");
                var canConnect = context.Database.CanConnect();
                logger.LogInformation("Can connect to database: {CanConnect}", canConnect);

                if (canConnect)
                {
                    logger.LogInformation("Attempting to migrate database...");
                    context.Database.Migrate();
                    logger.LogInformation("Database migration completed successfully");

                    // Seed data
                    logger.LogInformation("Starting SQL Server data seeding...");
                    await FY.WB.CSHero2.Infrastructure.Persistence.Seeders.DataSeeder.SeedAsync(services);
                    logger.LogInformation("SQL Server data seeding completed successfully");

                    // Seed CosmosDB data (after SQL Server seeding is complete)
                    logger.LogInformation("Starting CosmosDB data seeding...");
                    await SeedCosmosDbAsync(services, logger);
                    logger.LogInformation("CosmosDB data seeding completed successfully");
                }
                else
                {
                    logger.LogError("Cannot connect to database. Please check your connection string and SQL Server instance.");
                }
            }
            else
            {
                logger.LogWarning("Database is not SQL Server");
            }
        }
        catch (Exception innerEx)
        {
            logger.LogError(innerEx, "An error occurred during database initialization");
            if (innerEx.InnerException != null)
            {
                logger.LogError(innerEx.InnerException, "Inner exception during database initialization");
            }
        }
    }
}
catch (Exception ex)
{
    logger.LogError(ex, "An error occurred during database initialization");
    if (ex.InnerException != null)
    {
        logger.LogError(ex.InnerException, "Inner exception during database initialization");
    }
    logger.LogWarning("Application will proceed but database functionality may be limited");
}

// Routes
app.MapControllers();
app.MapGet("/api/ping", () =>
{
    logger.LogInformation("Ping endpoint called");
    return Results.Ok(new { message = "pong", timestamp = DateTime.UtcNow });
});

app.MapGet("/api/system-test", async () =>
{
    logger.LogInformation("System test endpoint called");
    try
    {
        await FY.WB.CSHero2.SimpleSystemTest.RunSimpleTestAsync();
        return Results.Ok(new {
            message = "System test completed successfully",
            timestamp = DateTime.UtcNow,
            status = "success"
        });
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "System test failed");
        return Results.Problem(
            detail: ex.Message,
            title: "System test failed",
            statusCode: 500
        );
    }
});

logger.LogInformation("API server starting up...");
app.Run();

// Helper method for CosmosDB seeding
static async Task SeedCosmosDbAsync(IServiceProvider services, ILogger logger)
{
    try
    {
        logger.LogInformation("Starting CosmosDB data seeding...");
        
        // Ensure CosmosDB containers are created first
        logger.LogInformation("Ensuring CosmosDB containers are created...");
        await EnsureCosmosDbCreatedAsync(services);
        logger.LogInformation("CosmosDB containers created successfully");

        // Get required services
        var styleService = services.GetService<IReportStyleService>();
        var tenantResolutionService = services.GetService<ITenantResolutionService>();
        var blobService = services.GetService<IReportDataBlobService>();
        
        if (styleService == null)
        {
            logger.LogWarning("IReportStyleService not available, skipping CosmosDB style seeding");
            return;
        }
        
        if (tenantResolutionService == null)
        {
            logger.LogWarning("ITenantResolutionService not available, skipping CosmosDB style seeding");
            return;
        }
        
        // Validate that SQL seeding has completed and tenants exist
        var tenants = await tenantResolutionService.GetSeededTenantsAsync();
        if (!tenants.Any())
        {
            logger.LogWarning("No tenants found in SQL database, skipping CosmosDB seeding");
            return;
        }
        
        logger.LogInformation("Found {TenantCount} tenants in SQL database, proceeding with CosmosDB seeding", tenants.Count);
        
        // Log tenant information for verification
        foreach (var tenant in tenants)
        {
            logger.LogInformation("Found tenant: {TenantId} - {CompanyName} ({ContactName})",
                tenant.TenantId, tenant.CompanyName, tenant.ContactName);
        }
        
        // Create and use the CosmosDbSeeder with tenant resolution
        var cosmosSeeder = new CosmosDbSeeder();
        var styleDocumentIds = await cosmosSeeder.SeedReportStylesAsync(
            styleService,
            tenantResolutionService,
            logger);

        logger.LogInformation("CosmosDB style seeding completed successfully. Created {Count} style documents",
            styleDocumentIds.Count);
            
        // Log created documents for verification
        foreach (var kvp in styleDocumentIds)
        {
            logger.LogInformation("Created style document: {Key} -> {DocumentId}", kvp.Key, kvp.Value);
        }

        if (blobService != null)
        {
            logger.LogInformation("IReportDataBlobService found, blob seeding ready for future implementation");
            // TODO: Implement blob storage seeding when ready
        }
        else
        {
            logger.LogInformation("IReportDataBlobService not available, skipping blob storage seeding");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "CosmosDB seeding failed");
        logger.LogWarning("Application will continue but CosmosDB functionality may be limited");
    }
}

// Helper method to ensure CosmosDB containers are created
static async Task EnsureCosmosDbCreatedAsync(IServiceProvider serviceProvider)
{
    var cosmosClient = serviceProvider.GetRequiredService<Microsoft.Azure.Cosmos.CosmosClient>();
    var options = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration.CosmosDbOptions>>().Value;

    // Create database if it doesn't exist
    var databaseResponse = await cosmosClient.CreateDatabaseIfNotExistsAsync(
        options.DatabaseName,
        throughput: 400); // Shared throughput for cost optimization

    var database = databaseResponse.Database;

    // Create containers if they don't exist
    await database.CreateContainerIfNotExistsAsync(
        options.Containers.ReportStyles,
        "/partitionKey",
        throughput: null); // Use shared database throughput
}
